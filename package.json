{"name": "linkedin-ai-assistant-chrome-extension", "version": "2.0.0", "description": "AI-powered LinkedIn comment and repost generator Chrome extension", "main": "contentscript.js", "scripts": {"lint": "eslint *.js", "lint:fix": "eslint *.js --fix", "validate": "web-ext lint", "build": "npm run validate && npm run package", "package": "web-ext build --source-dir . --artifacts-dir build --ignore-files='node_modules/**' '*.md' '.git/**' '.github/**' 'package*.json' 'setup-github.sh'", "test": "npm run lint && npm run validate", "dev": "npm run validate", "clean": "rm -rf build/ web-ext-artifacts/ node_modules/.cache"}, "keywords": ["linkedin", "ai", "chrome-extension", "automation", "social-media", "comments", "reposts", "artificial-intelligence"], "author": "LinkedIn AI Assistant Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/YOUR_USERNAME/linkedin-ai-assistant-chrome-extension.git"}, "bugs": {"url": "https://github.com/YOUR_USERNAME/linkedin-ai-assistant-chrome-extension/issues"}, "homepage": "https://github.com/YOUR_USERNAME/linkedin-ai-assistant-chrome-extension#readme", "devDependencies": {"eslint": "^8.55.0", "web-ext": "^7.8.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": ["Chrome >= 88"], "webExt": {"sourceDir": ".", "artifactsDir": "build", "ignoreFiles": ["node_modules/**", "*.md", ".git/**", ".github/**", "package*.json", "setup-github.sh", ".eslintrc.*"]}}