# LinkedIn AI Assistant Chrome Extension

A powerful Chrome extension that helps you generate AI-powered comment and repost suggestions for LinkedIn posts. The extension captures post content and existing comments, then sends them to your webhook for AI processing.

## ✨ Features

### 🎯 Core Functionality
- **Smart Comment Generation**: Capture LinkedIn posts and comments, add custom instructions, generate AI suggestions
- **Intelligent Repost Ideas**: Automatically generate repost suggestions with context
- **Real-time Data Preview**: See exactly what data is captured before sending to your webhook
- **Custom Instructions**: Add personalized instructions like "Make it more casual" or "Add emojis"

### 🔧 Technical Features
- **Up to 50 Comments**: Captures comprehensive comment context (increased from 10)
- **Fresh Data Capture**: Always gets current post data, no stale cache
- **Multiple Themes**: Modern, Light, Dark, and Neon themes
- **Flexible Positioning**: Left or right side placement
- **Error Handling**: Robust error handling and recovery

### 🎨 User Experience
- **Wait-for-Generate Flow**: Comments wait for your input before sending webhook
- **Visual Feedback**: Clear indication of captured data and processing status
- **Theme Integration**: Seamlessly matches LinkedIn's interface
- **Accessibility**: Full keyboard navigation and screen reader support

## 🚀 Quick Start

### Installation
1. Download or clone this repository
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension folder
5. The extension will appear in your Chrome toolbar

### Setup
1. Click the extension icon to open settings
2. Configure your webhook URL in the settings
3. Choose your preferred theme and position
4. Start using on LinkedIn!

## 📖 How to Use

### For Comments
1. **Navigate to LinkedIn** and find a post you want to comment on
2. **Click the comment button** (💬) next to the post
3. **Review captured data** - see the post content and existing comments
4. **Add instructions** (optional) - e.g., "Make it professional", "Add humor"
5. **Click "Generate Suggestions"** - sends data to your webhook
6. **Get AI suggestions** - use the generated comments

### For Reposts
1. **Click the repost button** (🔄) next to any post
2. **View captured data** - see post and comments preview
3. **Get suggestions automatically** - webhook is called immediately
4. **Use generated reposts** - copy and customize as needed

## 🔧 Configuration

### Webhook Setup
Your webhook will receive POST requests with this structure:

```json
{
  "postText": "Original LinkedIn post content...",
  "comments": ["Comment 1", "Comment 2", "..."],
  "action": "comment", // or "repost"
  "tone": "professional", // user's tone preference
  "additionalInstructions": "Make it more casual" // optional
}
```

### Themes
- **🔵 Modern**: Clean LinkedIn-style appearance
- **☀️ Light**: Professional light theme
- **🌑 Dark**: Dark theme with elegant styling
- **✨ Neon**: Cyberpunk-inspired bright colors

## 🛠️ Development

### Project Structure
```
linkedin-ai-assistant/
├── manifest.json          # Extension configuration
├── contentscript.js       # Main functionality
├── style.css             # Styling and themes
├── popup.html            # Extension popup
├── popup.js              # Popup functionality
└── README.md             # This file
```

### Key Components
- **Content Script**: Handles LinkedIn page interaction and data capture
- **Popup Interface**: Settings and configuration management
- **Theme System**: Dynamic styling based on user preferences
- **Webhook Integration**: Secure communication with your AI backend

### Building
No build process required - this is a vanilla JavaScript extension.

## 🔒 Privacy & Security

- **No Data Storage**: Extension doesn't store your LinkedIn data
- **Webhook Only**: Data is only sent to your configured webhook
- **Local Processing**: All data capture happens locally in your browser
- **No Tracking**: Extension doesn't track or monitor your activity

## 🐛 Troubleshooting

### Common Issues

**Extension not working on LinkedIn:**
- Refresh the LinkedIn page
- Check if extension is enabled in Chrome
- Verify webhook URL is configured

**No comments captured:**
- Make sure comments are visible on the page
- Try scrolling to load more comments
- Check browser console for error messages

**Webhook not receiving data:**
- Verify webhook URL is correct and accessible
- Check webhook server logs
- Ensure webhook accepts POST requests with JSON

### Debug Mode
Open browser console (F12) to see detailed logging:
- Data capture process
- Webhook communication
- Error messages and warnings

## 📝 Changelog

### v2.0.0 (Latest)
- ✨ Added custom instructions field for comments
- ✨ Implemented wait-for-generate flow
- ✨ Increased comment capture limit to 50
- ✨ Added real-time data preview
- 🐛 Fixed stale data issues
- 🐛 Removed icon dependencies
- 🎨 Enhanced UI feedback

### v1.0.0
- 🎉 Initial release
- Basic comment and repost generation
- Theme system
- Webhook integration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Issues**: Report bugs and request features on GitHub Issues
- **Documentation**: Check this README for detailed information
- **Community**: Join discussions in GitHub Discussions

## 🙏 Acknowledgments

- Built for LinkedIn professionals who want to enhance their engagement
- Designed with privacy and user control in mind
- Inspired by the need for contextual AI assistance

---

**Made with ❤️ for the LinkedIn community**
