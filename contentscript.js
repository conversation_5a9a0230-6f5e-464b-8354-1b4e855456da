// LinkedIn AI Assistant - Content Script
// Enhanced with drag and resize functionality

(function() {
    'use strict';

    // Global variables
    let currentTheme = 'modern';
    let currentPosition = 'right';
    let webhookUrl = '';
    let isQuotationBoxVisible = false;
    let isDragging = false;
    let isResizing = false;
    let dragOffset = { x: 0, y: 0 };
    let resizeStartSize = { width: 0, height: 0 };
    let resizeStartPos = { x: 0, y: 0 };
    let currentPostId = null;
    let isProcessing = false;
    let currentAction = null;
    let currentPostContainer = null;

    // FIXED: Default quotation box settings with better positioning
    let quotationBoxSettings = {
        position: { left: '50px', top: '50px' },
        size: { width: '480px', height: '600px' },
        isCustomPositioned: false
    };

    // Theme configurations
    const themes = {
        modern: {
            name: 'Modern',
            buttonClass: 'ai-enhancement-button-modern',
            quotationBoxClass: 'theme-modern'
        },
        light: {
            name: 'Light',
            buttonClass: 'ai-enhancement-button-light',
            quotationBoxClass: 'theme-light'
        },
        dark: {
            name: 'Dark',
            buttonClass: 'ai-enhancement-button-dark',
            quotationBoxClass: 'theme-dark'
        },
        neon: {
            name: 'Neon',
            buttonClass: 'ai-enhancement-button-neon',
            quotationBoxClass: 'theme-neon'
        }
    };

    // Initialize the extension
    function init() {
        loadSettings();
        observePageChanges();
        addGlobalButton();
        setupGlobalFunctions();
    }

    // Load settings from storage
    function loadSettings() {
        chrome.storage.sync.get(['theme', 'position', 'webhookUrl', 'quotationBoxSettings'], (result) => {
            currentTheme = result.theme || 'modern';
            currentPosition = result.position || 'right';
            webhookUrl = result.webhookUrl || '';
            
            // Load quotation box settings with defaults
            if (result.quotationBoxSettings) {
                quotationBoxSettings = {
                    ...quotationBoxSettings,
                    ...result.quotationBoxSettings
                };
            }
        });
    }

    // Save quotation box settings
    function saveQuotationBoxSettings() {
        chrome.storage.sync.set({
            quotationBoxSettings: quotationBoxSettings
        });
    }

    // Setup global functions for popup access
    function setupGlobalFunctions() {
        window.LinkedInAIAssistant_showQuotationBox = function(action = 'comment') {
            const postContainer = findNearestPostContainer(document.body);
            if (postContainer) {
                currentPostId = getPostId(postContainer);
                currentPostContainer = postContainer;
                showQuotationBox(action, postContainer);
            } else {
                showCustomAlert('Please click on a specific LinkedIn post first.');
            }
        };
    }

    // Observe page changes for dynamic content
    function observePageChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            addButtonsToNewPosts(node);
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Initial scan for existing posts
        setTimeout(() => {
            addButtonsToNewPosts(document.body);
        }, 1000);
    }

    // Add buttons to new posts
    function addButtonsToNewPosts(container) {
        const posts = container.querySelectorAll('[data-urn*="urn:li:activity"], [data-urn*="urn:li:ugcPost"], .feed-shared-update-v2, .occludable-update');
        
        posts.forEach(post => {
            if (!post.querySelector('.ai-enhancement-container')) {
                addButtonsToPost(post);
            }
        });
    }

    // Add AI enhancement buttons to a post
    function addButtonsToPost(postElement) {
        try {
            const socialActionsBar = postElement.querySelector('.social-actions-bar, .feed-shared-social-action-bar, .social-counts-reactions');
            
            if (!socialActionsBar) return;

            const commentButton = socialActionsBar.querySelector('[aria-label*="comment" i], [data-control-name="comment"], .comment-button, button[aria-label*="Comment"]');
            const repostButton = socialActionsBar.querySelector('[aria-label*="repost" i], [data-control-name="repost"], .repost-button, button[aria-label*="Repost"], button.social-reshare-button, button[aria-label*="republier" i]');

            if (commentButton) {
                addEnhancementButton(commentButton, 'comment', postElement);
            }
            if (repostButton) {
                addEnhancementButton(repostButton, 'repost', postElement);
            }
        } catch (error) {
            // Silent error handling
        }
    }

    // Add enhancement button to a specific action button
    function addEnhancementButton(actionButton, actionType, postElement) {
        let buttonParent;
        
        if (actionType === 'repost') {
            // For repost buttons, look for the specific dropdown container structure
            buttonParent = actionButton.closest('.artdeco-dropdown.feed-shared-social-action-bar__action-button') ||
                          actionButton.closest('.social-action-button') ||
                          actionButton.closest('button');
        } else {
            // For comment buttons, use the standard approach
            buttonParent = actionButton.closest('button, .social-action-button, .feed-shared-social-action-bar__action-button') || actionButton;
        }
        
        if (buttonParent.querySelector('.ai-enhancement-container')) return;

        const container = document.createElement('div');
        container.className = 'ai-enhancement-container';
        
        const enhancementButton = document.createElement('button');
        enhancementButton.className = `ai-enhancement-button ${themes[currentTheme].buttonClass}`;
        enhancementButton.innerHTML = '<span class="ai-enhancement-button-action-icon">🤖</span>';
        enhancementButton.setAttribute('aria-label', `AI ${actionType} suggestions`);
        enhancementButton.setAttribute('title', `Get AI-powered ${actionType} suggestions`);
        
        enhancementButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            if (isProcessing) return;
            
            const postId = getPostId(postElement);
            currentPostId = postId;
            currentPostContainer = postElement;
            showQuotationBox(actionType, postElement);
        });

        container.appendChild(enhancementButton);
        
        if (actionType === 'repost') {
            // For repost buttons, position above the entire dropdown container
            const dropdownContainer = buttonParent.closest('.artdeco-dropdown.feed-shared-social-action-bar__action-button');
            if (dropdownContainer) {
                dropdownContainer.style.position = 'relative';
                container.style.position = 'absolute';
                container.style.top = '-45px';
                container.style.left = '50%';
                container.style.transform = 'translateX(-50%)';
                container.style.zIndex = '10';
                dropdownContainer.appendChild(container);
            } else {
                // Fallback to standard positioning
                const parentContainer = buttonParent.parentElement;
                if (parentContainer) {
                    parentContainer.style.position = 'relative';
                    parentContainer.appendChild(container);
                }
            }
        } else {
            // Standard positioning for comment buttons
            const parentContainer = buttonParent.parentElement;
            if (parentContainer) {
                parentContainer.style.position = 'relative';
                parentContainer.appendChild(container);
            }
        }
    }

    // Add global floating button
    function addGlobalButton() {
        if (document.getElementById('linkedin-ai-ideas-button')) return;

        const globalButton = document.createElement('button');
        globalButton.id = 'linkedin-ai-ideas-button';
        globalButton.className = themes[currentTheme].quotationBoxClass;
        globalButton.innerHTML = `
            <div id="linkedin-ai-ideas-button-content">
                <span id="linkedin-ai-ideas-button-icon">🤖</span>
                <span id="linkedin-ai-ideas-button-text">AI Assistant</span>
            </div>
        `;
        globalButton.setAttribute('aria-label', 'Open LinkedIn AI Assistant');
        globalButton.setAttribute('title', 'Get AI-powered suggestions for LinkedIn posts');

        globalButton.addEventListener('click', () => {
            window.LinkedInAIAssistant_showQuotationBox('comment');
        });

        document.body.appendChild(globalButton);
    }

    // Get post ID from post element
    function getPostId(postElement) {
        if (!postElement) return null;
        
        // Try various methods to get a unique identifier
        const urn = postElement.getAttribute('data-urn');
        if (urn) return urn;
        
        const id = postElement.getAttribute('data-id');
        if (id) return id;
        
        const postId = postElement.id;
        if (postId) return postId;
        
        // Generate a unique ID based on post content
        const postText = postElement.querySelector('.feed-shared-text, .update-components-text');
        if (postText) {
            return 'post-' + postText.innerText.substring(0, 50).replace(/\s+/g, '-').toLowerCase();
        }
        
        return 'post-' + Date.now();
    }

    // Show quotation box with drag and resize functionality
    function showQuotationBox(action, postContainer) {
        if (isProcessing) return;
        
        isProcessing = true;
        currentAction = action;
        
        const postId = getPostId(postContainer);
        
        // Check if we're showing the same post
        const isSamePost = currentPostId === postId;
        
        if (isQuotationBoxVisible && isSamePost) {
            // Same post - update data and keep existing suggestions
            updatePostDataInBox(postContainer);
            isProcessing = false;
            return;
        }
        
        if (isQuotationBoxVisible && !isSamePost) {
            // Different post - close existing box first
            hideQuotationBox();
        }
        
        // Update current post ID
        currentPostId = postId;
        currentPostContainer = postContainer;
        
        const postData = capturePostData(postContainer);
        if (!postData.postText && !postData.comments.length) {
            showCustomAlert('No post content found. Please try clicking on a different post.');
            isProcessing = false;
            return;
        }

        createQuotationBox(action, postData, postContainer);
        isQuotationBoxVisible = true;
        isProcessing = false;
    }

    // Update post data in existing quotation box
    function updatePostDataInBox(postContainer) {
        const quotationBox = document.getElementById('quotationBox');
        if (!quotationBox) return;
        
        const postData = capturePostData(postContainer);
        const postInfoElement = quotationBox.querySelector('.quotation-box-post-info');
        
        if (postInfoElement) {
            const newPostInfo = createPostInfoHTML(postData);
            postInfoElement.innerHTML = newPostInfo;
        }
    }

    // FIXED: Create the quotation box with improved positioning and sizing
    function createQuotationBox(action, postData, postContainer) {
        // Remove existing box
        const existingBox = document.getElementById('quotationBox');
        if (existingBox) {
            existingBox.remove();
        }

        const quotationBox = document.createElement('div');
        quotationBox.id = 'quotationBox';
        quotationBox.className = `${themes[currentTheme].quotationBoxClass}`;
        
        // FIXED: Apply saved position and size with better defaults
        quotationBox.style.width = quotationBoxSettings.size.width;
        quotationBox.style.height = quotationBoxSettings.size.height;

        // FIXED: Better positioning logic
        if (quotationBoxSettings.isCustomPositioned) {
            quotationBox.style.left = quotationBoxSettings.position.left;
            quotationBox.style.top = quotationBoxSettings.position.top;
        } else {
            // FIXED: Better default positioning
            quotationBox.style.left = '50px';
            quotationBox.style.top = '50px';
        }

        quotationBox.innerHTML = `
            <div class="quotation-box-header-container">
                <div class="quotation-box-header">
                    <div class="quotation-box-icon">
                        <span class="quotation-box-icon-text">🤖</span>
                    </div>
                    <h3 class="quotation-box-title">AI ${action.charAt(0).toUpperCase() + action.slice(1)} Assistant</h3>
                    <div class="quotation-box-header-actions">
                        <button class="quotation-box-settings-button" aria-label="Settings">⚙️</button>
                        <button class="quotation-box-close-button" aria-label="Close">×</button>
                    </div>
                </div>
            </div>
            
            <div class="quotation-box-content">
                ${action === 'comment' ? `
                <div class="additional-instructions-container">
                    <label for="additionalInstructions" class="additional-instructions-label">
                        Custom Instructions (Optional)
                    </label>
                    <textarea 
                        id="additionalInstructions" 
                        class="additional-instructions-input"
                        placeholder="e.g., Make it more casual, Add emojis, Keep it professional..."
                        rows="2"
                    ></textarea>
                    <button class="generate-suggestions-button" id="generateSuggestions">
                        Generate ${action.charAt(0).toUpperCase() + action.slice(1)} Suggestions
                    </button>
                </div>
                ` : ''}

                <div class="quotation-box-post-info">
                    ${createPostInfoHTML(postData)}
                </div>

                <div class="suggestion-container" id="suggestionContainer">
                    <div style="text-align: center; padding: 40px; color: #666;">
                        ${action === 'comment' ? 'Click "Generate Suggestions" to get AI-powered comment ideas' : 'Generating repost suggestions...'}
                    </div>
                </div>
            </div>

            <!-- Resize handles -->
            <div class="resize-handle resize-handle-se" data-direction="se"></div>
            <div class="resize-handle resize-handle-s" data-direction="s"></div>
            <div class="resize-handle resize-handle-e" data-direction="e"></div>
        `;

        document.body.appendChild(quotationBox);

        // FIXED: Setup drag functionality with improved constraints
        setupDragFunctionality(quotationBox);
        
        // Setup resize functionality
        setupResizeFunctionality(quotationBox);

        // Setup event listeners
        setupQuotationBoxEventListeners(quotationBox, action, postData, postContainer);

        // Show with animation
        setTimeout(() => {
            quotationBox.classList.add('visible');
        }, 10);

        // Setup keyboard shortcuts
        setupKeyboardShortcuts(quotationBox);

        // For reposts, auto-generate suggestions
        if (action === 'repost') {
            generateSuggestions(action, postData, '');
        }
    }

    // Create post info HTML
    function createPostInfoHTML(postData) {
        return `
            <strong>Captured Post Data:</strong>
            <div class="quotation-box-post-preview">${postData.postText || 'No post text captured'}</div>
            ${postData.comments.length > 0 ? `
                <div style="margin-top: 10px;">
                    <strong>Comments (${postData.comments.length}):</strong>
                    <div style="font-size: 12px; opacity: 0.8; margin-top: 5px;">
                        ${postData.comments.slice(0, 3).map(comment => `"${comment.substring(0, 50)}..."`).join(', ')}
                        ${postData.comments.length > 3 ? ` and ${postData.comments.length - 3} more` : ''}
                    </div>
                </div>
            ` : '<div style="margin-top: 10px; opacity: 0.7;">No comments captured</div>'}
        `;
    }

    // Setup quotation box event listeners (CSP compliant)
    function setupQuotationBoxEventListeners(quotationBox, action, postData, postContainer) {
        // Close button
        const closeButton = quotationBox.querySelector('.quotation-box-close-button');
        if (closeButton) {
            closeButton.addEventListener('click', hideQuotationBox);
        }

        // Settings button
        const settingsButton = quotationBox.querySelector('.quotation-box-settings-button');
        if (settingsButton) {
            settingsButton.addEventListener('click', () => toggleSettings(quotationBox));
        }

        // Generate button (for comments only)
        if (action === 'comment') {
            const generateButton = quotationBox.querySelector('#generateSuggestions');
            if (generateButton) {
                generateButton.addEventListener('click', () => {
                    // Update post data before generating
                    const freshPostData = capturePostData(postContainer);
                    updatePostDataInBox(postContainer);
                    
                    const instructions = quotationBox.querySelector('#additionalInstructions').value;
                    
                    // Check if we already have suggestions - if so, prepend new ones
                    const existingSuggestions = quotationBox.querySelectorAll('.suggestion-item');
                    const shouldPrepend = existingSuggestions.length > 0;
                    
                    generateSuggestions(action, freshPostData, instructions, shouldPrepend);
                });
            }
        }
    }

    // FIXED: Setup drag functionality with better viewport constraints
    function setupDragFunctionality(quotationBox) {
        const header = quotationBox.querySelector('.quotation-box-header-container');
        
        header.addEventListener('mousedown', startDrag);
        
        function startDrag(e) {
            if (e.target.closest('.quotation-box-settings-button') || e.target.closest('.quotation-box-close-button')) {
                return;
            }
            
            isDragging = true;
            quotationBox.classList.add('dragging');
            
            const rect = quotationBox.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;
            
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDrag);
            
            e.preventDefault();
        }
        
        function drag(e) {
            if (!isDragging) return;
            
            const newX = e.clientX - dragOffset.x;
            const newY = e.clientY - dragOffset.y;
            
            // FIXED: Better viewport constraints
            const boxRect = quotationBox.getBoundingClientRect();
            const maxX = window.innerWidth - boxRect.width;
            const maxY = window.innerHeight - boxRect.height;
            
            const constrainedX = Math.max(0, Math.min(newX, maxX));
            const constrainedY = Math.max(0, Math.min(newY, maxY));
            
            quotationBox.style.left = constrainedX + 'px';
            quotationBox.style.top = constrainedY + 'px';
        }
        
        function stopDrag() {
            if (isDragging) {
                // Save the new position
                quotationBoxSettings.position.left = quotationBox.style.left;
                quotationBoxSettings.position.top = quotationBox.style.top;
                quotationBoxSettings.isCustomPositioned = true;
                saveQuotationBoxSettings();
            }
            
            isDragging = false;
            quotationBox.classList.remove('dragging');
            document.removeEventListener('mousemove', drag);
            document.removeEventListener('mouseup', stopDrag);
        }
    }

    // Setup resize functionality with size saving
    function setupResizeFunctionality(quotationBox) {
        const resizeHandles = quotationBox.querySelectorAll('.resize-handle');
        
        resizeHandles.forEach(handle => {
            handle.addEventListener('mousedown', startResize);
        });
        
        function startResize(e) {
            isResizing = true;
            const direction = e.target.dataset.direction;
            
            const rect = quotationBox.getBoundingClientRect();
            resizeStartSize.width = rect.width;
            resizeStartSize.height = rect.height;
            resizeStartPos.x = e.clientX;
            resizeStartPos.y = e.clientY;
            
            document.addEventListener('mousemove', resize);
            document.addEventListener('mouseup', stopResize);
            
            e.preventDefault();
            e.stopPropagation();
            
            function resize(e) {
                if (!isResizing) return;
                
                const deltaX = e.clientX - resizeStartPos.x;
                const deltaY = e.clientY - resizeStartPos.y;
                
                let newWidth = resizeStartSize.width;
                let newHeight = resizeStartSize.height;
                
                if (direction.includes('e')) {
                    newWidth = resizeStartSize.width + deltaX;
                }
                if (direction.includes('s')) {
                    newHeight = resizeStartSize.height + deltaY;
                }
                
                // Apply constraints
                newWidth = Math.max(320, Math.min(newWidth, window.innerWidth * 0.95));
                newHeight = Math.max(400, Math.min(newHeight, window.innerHeight * 0.9));
                
                quotationBox.style.width = newWidth + 'px';
                quotationBox.style.height = newHeight + 'px';
            }
        }
        
        function stopResize() {
            if (isResizing) {
                // Save the new size
                quotationBoxSettings.size.width = quotationBox.style.width;
                quotationBoxSettings.size.height = quotationBox.style.height;
                saveQuotationBoxSettings();
            }
            
            isResizing = false;
            document.removeEventListener('mousemove', resize);
            document.removeEventListener('mouseup', stopResize);
        }
    }

    // Setup keyboard shortcuts
    function setupKeyboardShortcuts(quotationBox) {
        function handleKeydown(e) {
            if (e.key === 'Escape') {
                hideQuotationBox();
            } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                const generateButton = quotationBox.querySelector('#generateSuggestions');
                if (generateButton) {
                    generateButton.click();
                }
            }
        }
        
        document.addEventListener('keydown', handleKeydown);
        
        // Store reference to remove listener later
        quotationBox._keydownHandler = handleKeydown;
    }

    // Hide quotation box
    function hideQuotationBox() {
        const quotationBox = document.getElementById('quotationBox');
        if (quotationBox) {
            // Remove keyboard listener
            if (quotationBox._keydownHandler) {
                document.removeEventListener('keydown', quotationBox._keydownHandler);
            }
            
            quotationBox.style.opacity = '0';
            quotationBox.style.transform = 'scale(0.9)';
            
            setTimeout(() => {
                quotationBox.remove();
            }, 300);
        }
        isQuotationBoxVisible = false;
        isProcessing = false;
        currentAction = null;
    }

    // Capture post data
    function capturePostData(postContainer) {
        const postData = {
            postText: '',
            comments: [],
            postId: '',
            authorName: '',
            timestamp: new Date().toISOString()
        };

        try {
            // Extract post text
            const postTextSelectors = [
                '.feed-shared-text',
                '.feed-shared-update-v2__description',
                '[data-test-id="main-feed-activity-card"] .break-words',
                '.feed-shared-inline-show-more-text',
                '.attributed-text-segment-list__content'
            ];

            for (const selector of postTextSelectors) {
                const textElement = postContainer.querySelector(selector);
                if (textElement) {
                    postData.postText = textElement.innerText.trim();
                    break;
                }
            }

            // Extract post ID
            const postIdAttr = postContainer.getAttribute('data-urn') || 
                              postContainer.getAttribute('data-id') ||
                              postContainer.querySelector('[data-urn]')?.getAttribute('data-urn');
            if (postIdAttr) {
                postData.postId = postIdAttr;
            }

            // Extract author name
            const authorSelectors = [
                '.feed-shared-actor__name',
                '.update-components-actor__name',
                '.feed-shared-actor__title'
            ];

            for (const selector of authorSelectors) {
                const authorElement = postContainer.querySelector(selector);
                if (authorElement) {
                    postData.authorName = authorElement.innerText.trim();
                    break;
                }
            }

            // Extract comments
            const commentSelectors = [
                '.comments-comment-item__main-content',
                '.comment-thread-item__main-content',
                '.comments-comment-textual-content'
            ];

            const commentElements = postContainer.querySelectorAll(commentSelectors.join(', '));
            postData.comments = Array.from(commentElements)
                .map(el => el.innerText.trim())
                .filter(text => text.length > 0)
                .slice(0, 50); // Limit to 50 comments

        } catch (error) {
            // Silent error handling
        }

        return postData;
    }

    // Find nearest post container
    function findNearestPostContainer(element) {
        const postSelectors = [
            '[data-urn*="urn:li:activity"]',
            '[data-urn*="urn:li:ugcPost"]',
            '.feed-shared-update-v2',
            '.occludable-update',
            '.feed-shared-update-v2__content'
        ];

        let current = element;
        while (current && current !== document.body) {
            for (const selector of postSelectors) {
                if (current.matches && current.matches(selector)) {
                    return current;
                }
                const found = current.querySelector(selector);
                if (found) {
                    return found;
                }
            }
            current = current.parentElement;
        }

        // Fallback: find any post on the page
        for (const selector of postSelectors) {
            const found = document.querySelector(selector);
            if (found) {
                return found;
            }
        }

        return null;
    }

    // FIXED: Generate suggestions with improved webhook response handling
    async function generateSuggestions(action, postData, additionalInstructions = '', prependSuggestions = false) {
        const suggestionContainer = document.getElementById('suggestionContainer');
        if (!suggestionContainer) return;

        // Show loading state
        if (!prependSuggestions) {
            suggestionContainer.innerHTML = '<div class="loading-spinner"></div>';
        } else {
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'loading-spinner';
            suggestionContainer.insertBefore(loadingDiv, suggestionContainer.firstChild);
        }

        // FIXED: Improved webhook response handling as provided by user
        try {
            if (!webhookUrl) {
                throw new Error('Webhook URL not configured. Please set it in the extension settings.');
            }

            const payload = {
                postText: postData.postText,
                comments: postData.comments,
                action: action,
                tone: 'professional',
                additionalInstructions: additionalInstructions,
                postId: postData.postId,
                authorName: postData.authorName,
                timestamp: postData.timestamp
            };

            const response = await fetch(webhookUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`);
            }

            const responseText = await response.text();
            let result;

            // **FIX START**: Intelligently handle response format
            const trimmedResponse = responseText.trim();
            if (trimmedResponse.startsWith('{') || trimmedResponse.startsWith('[')) {
                // Response looks like JSON, attempt to parse
                console.log("Attempting to parse response as JSON.");
                try {
                    result = JSON.parse(trimmedResponse);
                } catch (jsonError) {
                    console.error("Failed to parse response as JSON:", jsonError);
                    // Fallback for malformed JSON: show an error suggestion
                    result = { suggestions: ["Error: Received malformed JSON from webhook."] };
                }
            } else {
                // Response is likely plain text, split by newlines
                console.log("Response is not JSON, treating as newline-separated text.");
                const suggestionsArray = trimmedResponse.split("\n").filter(line => line.trim() !== "");
                result = { suggestions: suggestionsArray };
            }
            // **FIX END**

            displaySuggestions(result, action, prependSuggestions);

        } catch (error) {
            const loadingSpinner = suggestionContainer.querySelector('.loading-spinner');
            if (loadingSpinner) {
                loadingSpinner.remove();
            }
            
            suggestionContainer.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #ef4444;">
                    <strong>Error:</strong> ${error.message}
                    <br><br>
                    <small>Check the console for more details.</small>
                </div>
            `;
        }
    }

    // Display suggestions - Different handling for comments vs reposts
    function displaySuggestions(result, action, prependSuggestions = false) {
        const suggestionContainer = document.getElementById('suggestionContainer');
        if (!suggestionContainer) return;

        // Remove loading spinner
        const loadingSpinner = suggestionContainer.querySelector('.loading-spinner');
        if (loadingSpinner) {
            loadingSpinner.remove();
        }

        let suggestions = [];

        // Handle different response formats
        if (Array.isArray(result)) {
            suggestions = result;
        } else if (result.suggestions && Array.isArray(result.suggestions)) {
            suggestions = result.suggestions;
        } else if (result.comments && Array.isArray(result.comments)) {
            suggestions = result.comments;
        } else if (result.reposts && Array.isArray(result.reposts)) {
            suggestions = result.reposts;
        } else if (typeof result === 'string') {
            suggestions = [result];
        }

        if (suggestions.length === 0) {
            suggestionContainer.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #666;">
                    No suggestions received. Please check your webhook response format.
                </div>
            `;
            return;
        }

        if (prependSuggestions) {
            // Add separator for new suggestions
            const separator = document.createElement('div');
            separator.className = 'suggestions-separator';
            separator.innerHTML = `<span class="suggestions-separator-label">New Suggestions</span>`;
            suggestionContainer.insertBefore(separator, suggestionContainer.firstChild);
        } else {
            // Clear existing content for fresh suggestions
            suggestionContainer.innerHTML = '';
        }

        // Handle reposts differently - show full text together
        if (action === 'repost') {
            // Combine all suggestions into one continuous text
            const combinedText = suggestions.map(suggestion => {
                return typeof suggestion === 'string' ? suggestion : suggestion.text || suggestion.content || JSON.stringify(suggestion);
            }).join('\n\n');

            const repostCard = document.createElement('div');
            repostCard.className = 'suggestion-item';

            repostCard.innerHTML = `
                <div class="suggestion-header">
                    <h5>Repost Content</h5>
                    <button class="suggestion-copy-button">
                        📋 Copy All
                    </button>
                </div>
                <div class="suggestion-text" style="white-space: pre-wrap;">${combinedText}</div>
            `;

            // Add copy functionality for the combined text
            const copyButton = repostCard.querySelector('.suggestion-copy-button');
            copyButton.addEventListener('click', () => {
                copyToClipboard(combinedText);
            });

            if (prependSuggestions) {
                suggestionContainer.insertBefore(repostCard, suggestionContainer.firstChild);
            } else {
                suggestionContainer.appendChild(repostCard);
            }
        } else {
            // For comments: Create each suggestion as a separate card (existing behavior)
            suggestions.forEach((suggestion, index) => {
                const suggestionText = typeof suggestion === 'string' ? suggestion : suggestion.text || suggestion.content || JSON.stringify(suggestion);

                const suggestionCard = document.createElement('div');
                suggestionCard.className = 'suggestion-item';

                suggestionCard.innerHTML = `
                    <div class="suggestion-header">
                        <h5>${action.charAt(0).toUpperCase() + action.slice(1)} Suggestion ${index + 1}</h5>
                        <button class="suggestion-copy-button">
                            📋 Copy
                        </button>
                    </div>
                    <div class="suggestion-text">${suggestionText}</div>
                    <div style="padding: 12px; border-top: 1px solid var(--border-primary); background-color: var(--bg-secondary);">
                        <button class="suggestion-insert-button">
                            💬 Insert Comment
                        </button>
                    </div>
                `;

                // Add event listeners to this specific card
                const copyButton = suggestionCard.querySelector('.suggestion-copy-button');
                copyButton.addEventListener('click', () => {
                    copyToClipboard(suggestionText);
                });

                const insertButton = suggestionCard.querySelector('.suggestion-insert-button');
                insertButton.addEventListener('click', () => {
                    insertSelectedComment(suggestionText);
                });

                // Append each card individually
                if (prependSuggestions) {
                    suggestionContainer.insertBefore(suggestionCard, suggestionContainer.firstChild);
                } else {
                    suggestionContainer.appendChild(suggestionCard);
                }
            });
        }

        // Add confetti effect
        createConfetti();
    }

    // Copy to clipboard function
    function copyToClipboard(text) {
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(text).then(() => {
                showCustomAlert('Copied to clipboard!');
            }).catch(err => {
                fallbackCopyToClipboard(text);
            });
        } else {
            fallbackCopyToClipboard(text);
        }
    }

    // Fallback copy method for older browsers
    function fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            showCustomAlert('Copied to clipboard!');
        } catch (err) {
            showCustomAlert('Failed to copy text. Please copy manually.');
        }
        
        document.body.removeChild(textArea);
    }

    // Insert selected comment
    function insertSelectedComment(comment) {
        // Find the comment box for the current post
        let commentBox = null;
        
        // First, try to find comment box within the current post container
        if (currentPostContainer) {
            const postCommentSelectors = [
                '.ql-editor[data-placeholder*="comment" i]',
                '.ql-editor[data-placeholder*="Ajouter" i]', // French
                '.comments-comment-texteditor .ql-editor',
                'div[role="textbox"][contenteditable="true"]',
                '.comments-comment-box .ql-editor'
            ];

            for (const selector of postCommentSelectors) {
                commentBox = currentPostContainer.querySelector(selector);
                if (commentBox && commentBox.offsetParent !== null) { // Check if visible
                    break;
                }
                commentBox = null;
            }
        }
        
        // If not found in post container, look globally for visible comment boxes
        if (!commentBox) {
            const globalCommentSelectors = [
                '.ql-editor[data-placeholder*="comment" i]',
                '.ql-editor[data-placeholder*="Ajouter" i]', // French
                '.comments-comment-texteditor .ql-editor',
                'div[role="textbox"][contenteditable="true"]',
                '.comments-comment-box .ql-editor'
            ];

            for (const selector of globalCommentSelectors) {
                const boxes = document.querySelectorAll(selector);
                for (const box of boxes) {
                    if (box.offsetParent !== null) { // Check if visible
                        commentBox = box;
                        break;
                    }
                }
                if (commentBox) break;
            }
        }

        if (commentBox) {
            // Create a paragraph element with the comment text
            const paragraph = document.createElement('p');
            paragraph.textContent = comment;
            
            // Clear existing content and insert the new paragraph
            commentBox.innerHTML = '';
            commentBox.appendChild(paragraph);
            
            // Set cursor at the end
            const range = document.createRange();
            const selection = window.getSelection();
            range.selectNodeContents(paragraph);
            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);
            
            // Focus the comment box
            commentBox.focus();
            
            // Trigger LinkedIn's events
            commentBox.dispatchEvent(new Event('input', { bubbles: true }));
            commentBox.dispatchEvent(new Event('change', { bubbles: true }));
            
            showCustomAlert('Comment inserted successfully!');
        } else {
            showCustomAlert('Could not find comment box. Please click on "Add a comment" first.');
        }
    }

    // Create confetti effect
    function createConfetti() {
        const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];
        
        for (let i = 0; i < 50; i++) {
            setTimeout(() => {
                const confetti = document.createElement('div');
                confetti.className = 'confetti';
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.animationDuration = (Math.random() * 3 + 2) + 's';
                confetti.style.animationDelay = Math.random() * 2 + 's';
                
                document.body.appendChild(confetti);
                
                setTimeout(() => {
                    confetti.remove();
                }, 5000);
            }, i * 50);
        }
    }

    // Toggle settings panel
    function toggleSettings(quotationBox) {
        let settingsPanel = quotationBox.querySelector('.settings-panel');
        
        if (settingsPanel) {
            settingsPanel.remove();
            return;
        }

        settingsPanel = document.createElement('div');
        settingsPanel.className = 'settings-panel';
        settingsPanel.innerHTML = `
            <div class="settings-group">
                <label class="settings-label">Theme</label>
                <select class="settings-select" id="themeSelect">
                    ${Object.entries(themes).map(([key, theme]) => 
                        `<option value="${key}" ${key === currentTheme ? 'selected' : ''}>${theme.name}</option>`
                    ).join('')}
                </select>
            </div>
            
            <div class="settings-group">
                <label class="settings-label">Position</label>
                <div class="settings-position-options">
                    <div class="settings-position-option ${currentPosition === 'left' ? 'selected' : ''}" data-position="left">Left</div>
                    <div class="settings-position-option ${currentPosition === 'right' ? 'selected' : ''}" data-position="right">Right</div>
                </div>
            </div>
            
            <div class="settings-group">
                <label class="settings-label">Webhook URL</label>
                <input type="url" class="settings-select" id="webhookInput" value="${webhookUrl}" placeholder="https://your-webhook-url.com">
            </div>
            
            <div class="settings-group">
                <button class="settings-reset-button" id="resetPosition">
                    Reset Position & Size
                </button>
            </div>
            
            <button class="settings-save-button" id="saveSettings">Save Settings</button>
        `;

        quotationBox.querySelector('.quotation-box-content').appendChild(settingsPanel);

        // Setup settings event listeners
        setupSettingsEventListeners(settingsPanel, quotationBox);
    }

    // Setup settings event listeners
    function setupSettingsEventListeners(settingsPanel, quotationBox) {
        const themeSelect = settingsPanel.querySelector('#themeSelect');
        const positionOptions = settingsPanel.querySelectorAll('.settings-position-option');
        const webhookInput = settingsPanel.querySelector('#webhookInput');
        const saveButton = settingsPanel.querySelector('#saveSettings');
        const resetButton = settingsPanel.querySelector('#resetPosition');

        themeSelect.addEventListener('change', (e) => {
            currentTheme = e.target.value;
            updateTheme();
        });

        positionOptions.forEach(option => {
            option.addEventListener('click', () => {
                positionOptions.forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
                currentPosition = option.dataset.position;
            });
        });

        resetButton.addEventListener('click', () => {
            // Reset to default position and size
            quotationBoxSettings = {
                position: { left: '50px', top: '50px' },
                size: { width: '480px', height: '600px' },
                isCustomPositioned: false
            };
            
            // Apply immediately
            quotationBox.style.left = '50px';
            quotationBox.style.top = '50px';
            quotationBox.style.width = '480px';
            quotationBox.style.height = '600px';
            
            saveQuotationBoxSettings();
            showCustomAlert('Position and size reset to default!');
        });

        saveButton.addEventListener('click', () => {
            webhookUrl = webhookInput.value;
            
            chrome.storage.sync.set({
                theme: currentTheme,
                position: currentPosition,
                webhookUrl: webhookUrl
            }, () => {
                showCustomAlert('Settings saved successfully!');
                settingsPanel.remove();
            });
        });
    }

    // Update theme
    function updateTheme() {
        const quotationBox = document.getElementById('quotationBox');
        if (quotationBox) {
            // Remove old theme classes
            Object.values(themes).forEach(theme => {
                quotationBox.classList.remove(theme.quotationBoxClass);
            });
            // Add new theme class
            quotationBox.classList.add(themes[currentTheme].quotationBoxClass);
        }

        // Update global button
        const globalButton = document.getElementById('linkedin-ai-ideas-button');
        if (globalButton) {
            globalButton.className = themes[currentTheme].quotationBoxClass;
        }

        // Update enhancement buttons
        document.querySelectorAll('.ai-enhancement-button').forEach(button => {
            Object.values(themes).forEach(theme => {
                button.classList.remove(theme.buttonClass);
            });
            button.classList.add(themes[currentTheme].buttonClass);
        });
    }

    // Show custom alert
    function showCustomAlert(message) {
        const overlay = document.createElement('div');
        overlay.className = 'custom-alert-overlay';
        
        const alertBox = document.createElement('div');
        alertBox.className = `custom-alert-box ${themes[currentTheme].quotationBoxClass}`;
        
        alertBox.innerHTML = `
            <div class="custom-alert-message">${message}</div>
            <button class="custom-alert-button">OK</button>
        `;
        
        overlay.appendChild(alertBox);
        document.body.appendChild(overlay);
        
        const okButton = alertBox.querySelector('.custom-alert-button');
        okButton.addEventListener('click', () => {
            overlay.classList.remove('visible');
            setTimeout(() => overlay.remove(), 300);
        });
        
        setTimeout(() => overlay.classList.add('visible'), 10);
        
        // Auto-close after 5 seconds
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.classList.remove('visible');
                setTimeout(() => overlay.remove(), 300);
            }
        }, 5000);
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();