#!/bin/bash

# LinkedIn AI Assistant Chrome Extension - GitHub Setup Script
# This script helps you set up the GitHub repository and push your code

echo "🚀 LinkedIn AI Assistant Chrome Extension - GitHub Setup"
echo "======================================================="

# Check if git is installed
if ! command -v git &> /dev/null; then
    echo "❌ Git is not installed. Please install Git first."
    exit 1
fi

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo "📁 Initializing Git repository..."
    git init
    echo "✅ Git repository initialized"
else
    echo "✅ Git repository already exists"
fi

# Add all files to git
echo "📝 Adding files to Git..."
git add .

# Create initial commit if no commits exist
if ! git rev-parse --verify HEAD >/dev/null 2>&1; then
    echo "💾 Creating initial commit..."
    git commit -m "feat: initial commit with LinkedIn AI Assistant Chrome Extension

✨ Features:
- Custom instructions field for comments
- Enhanced comment capture (up to 50 comments)
- Real-time data preview
- Wait-for-generate flow
- Multiple themes support
- Webhook integration
- Fresh data capture system

🐛 Fixes:
- Removed icon dependencies
- Fixed timing issues
- Resolved stale data problems
- Updated header structure

📚 Documentation:
- Comprehensive README
- Contributing guidelines
- GitHub Actions CI/CD
- Issue templates"
    echo "✅ Initial commit created"
else
    echo "✅ Git repository has existing commits"
fi

# Check if remote origin exists
if ! git remote get-url origin >/dev/null 2>&1; then
    echo ""
    echo "🔗 GitHub Repository Setup"
    echo "=========================="
    echo ""
    echo "To complete the setup, you need to:"
    echo ""
    echo "1. Create a new repository on GitHub:"
    echo "   - Go to https://github.com/new"
    echo "   - Repository name: linkedin-ai-assistant-chrome-extension"
    echo "   - Description: AI-powered LinkedIn comment and repost generator"
    echo "   - Make it Public (recommended) or Private"
    echo "   - Don't initialize with README (we already have one)"
    echo ""
    echo "2. Add the remote origin:"
    echo "   git remote add origin https://github.com/YOUR_USERNAME/linkedin-ai-assistant-chrome-extension.git"
    echo ""
    echo "3. Push to GitHub:"
    echo "   git branch -M main"
    echo "   git push -u origin main"
    echo ""
    echo "4. Set up branch protection (optional but recommended):"
    echo "   - Go to Settings > Branches in your GitHub repo"
    echo "   - Add rule for 'main' branch"
    echo "   - Enable 'Require status checks to pass before merging'"
    echo "   - Enable 'Require pull request reviews before merging'"
    echo ""
else
    echo "✅ Remote origin already configured"
    echo "🚀 Pushing to GitHub..."
    
    # Push to main branch
    git branch -M main
    git push -u origin main
    
    echo "✅ Code pushed to GitHub successfully!"
fi

echo ""
echo "📋 Next Steps:"
echo "=============="
echo ""
echo "1. 🔧 Configure GitHub Actions:"
echo "   - The CI/CD pipeline will run automatically on push"
echo "   - Check the Actions tab in your GitHub repository"
echo ""
echo "2. 📝 Update README if needed:"
echo "   - Add your specific webhook URL documentation"
echo "   - Update any repository-specific information"
echo ""
echo "3. 🏷️ Create your first release:"
echo "   - Go to Releases in your GitHub repo"
echo "   - Click 'Create a new release'"
echo "   - Tag version: v2.0.0"
echo "   - Release title: LinkedIn AI Assistant v2.0.0"
echo "   - Describe the features and improvements"
echo ""
echo "4. 🌟 Enable GitHub features:"
echo "   - Issues (for bug reports and feature requests)"
echo "   - Discussions (for community questions)"
echo "   - Wiki (for detailed documentation)"
echo ""
echo "5. 🔒 Security settings:"
echo "   - Enable Dependabot alerts"
echo "   - Set up code scanning (if public repo)"
echo "   - Review security advisories"
echo ""
echo "✨ Your LinkedIn AI Assistant Chrome Extension is ready for GitHub!"
echo ""
echo "Repository features included:"
echo "- 📚 Comprehensive documentation"
echo "- 🤖 GitHub Actions CI/CD pipeline"
echo "- 🐛 Issue templates for bugs and features"
echo "- 🔄 Pull request template"
echo "- 📋 Contributing guidelines"
echo "- 📄 MIT License"
echo "- 📝 Changelog tracking"
echo ""
echo "Happy coding! 🎉"
