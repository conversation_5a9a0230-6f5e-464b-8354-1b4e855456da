---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: bug
assignees: ''

---

## 🐛 Bug Description
A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## ✅ Expected Behavior
A clear and concise description of what you expected to happen.

## ❌ Actual Behavior
A clear and concise description of what actually happened.

## 📸 Screenshots
If applicable, add screenshots to help explain your problem.

## 🌐 Environment
- **Browser**: [e.g. Chrome 120.0.6099.109]
- **Extension Version**: [e.g. 2.0.0]
- **Operating System**: [e.g. Windows 11, macOS 14.1]
- **LinkedIn Page**: [e.g. Feed, Post detail, Company page]

## 📋 Additional Context
- **Webhook URL configured**: [Yes/No]
- **Theme used**: [Modern/Light/Dark/Neon]
- **Position**: [Left/Right]
- **Console errors**: [Any error messages from browser console]

## 🔍 Console Logs
If applicable, paste any relevant console logs here:
```
Paste console logs here
```

## 🤔 Possible Solution
If you have ideas on how to fix this, please share them here.

## 📝 Additional Information
Add any other context about the problem here.
