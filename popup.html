<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn AI Assistant</title>
    <style>
        :root {
            --linkedin-blue: #0a66c2;
            --linkedin-blue-hover: #004182;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-600: #4b5563;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --radius-lg: 0.5rem;
            --radius-xl: 0.75rem;
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --transition-fast: 150ms ease;
            --font-family: -apple-system, BlinkMacSystemFont, "Se<PERSON><PERSON> UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
        }

        * {
            box-sizing: border-box;
        }

        body {
            width: 320px;
            min-height: 400px;
            font-family: var(--font-family);
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, var(--gray-50) 0%, #ffffff 100%);
            color: var(--gray-900);
            overflow: hidden;
        }

        .popup-container {
            padding: var(--space-6);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            min-height: 400px;
            justify-content: center;
        }

        .popup-header {
            margin-bottom: var(--space-6);
        }

        .popup-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, var(--linkedin-blue), var(--linkedin-blue-hover));
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-4);
            box-shadow: var(--shadow-lg);
            color: white;
            font-size: 28px;
            font-weight: 600;
        }

        .popup-title {
            margin: 0 0 var(--space-2);
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-900);
            line-height: 1.2;
        }

        .popup-subtitle {
            margin: 0;
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .popup-content {
            margin-bottom: var(--space-8);
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .popup-description {
            font-size: 1rem;
            line-height: 1.6;
            color: var(--gray-600);
            margin-bottom: var(--space-6);
            max-width: 280px;
        }

        .popup-features {
            list-style: none;
            padding: 0;
            margin: 0 0 var(--space-6);
        }

        .popup-feature {
            display: flex;
            align-items: center;
            margin-bottom: var(--space-3);
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .popup-feature-icon {
            width: 20px;
            height: 20px;
            background-color: var(--linkedin-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--space-3);
            color: white;
            font-size: 12px;
            flex-shrink: 0;
        }

        .popup-button {
            width: 100%;
            padding: var(--space-4) var(--space-6);
            background: linear-gradient(135deg, var(--linkedin-blue), var(--linkedin-blue-hover));
            color: white;
            border: none;
            border-radius: var(--radius-lg);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-fast);
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .popup-button::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, white, white);
            opacity: 0;
            transition: opacity var(--transition-fast);
        }

        .popup-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        .popup-button:hover::before {
            opacity: 0.1;
        }

        .popup-button:active {
            transform: translateY(0);
        }

        .popup-button-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            position: relative;
            z-index: 1;
        }

        .popup-button-icon {
            font-size: 1.125rem;
        }

        .popup-footer {
            margin-top: auto;
            padding-top: var(--space-4);
            border-top: 1px solid var(--gray-100);
        }

        .popup-footer-text {
            font-size: 0.75rem;
            color: var(--gray-600);
            margin: 0;
            line-height: 1.4;
        }

        .popup-status {
            margin-top: var(--space-4);
            padding: var(--space-3);
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
            text-align: center;
            display: none;
        }

        .popup-status.success {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .popup-status.error {
            background-color: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        /* Animation for smooth entrance */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .popup-container {
            animation: slideIn 0.3s ease-out;
        }

        /* Responsive adjustments */
        @media (max-height: 500px) {
            .popup-container {
                padding: var(--space-4);
                min-height: auto;
            }
            
            .popup-icon {
                width: 48px;
                height: 48px;
                font-size: 20px;
            }
            
            .popup-title {
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body>
    <div class="popup-container">
        <header class="popup-header">
            <div class="popup-icon">
                <span>🤖</span>
            </div>
            <h1 class="popup-title">LinkedIn AI Assistant</h1>
            <p class="popup-subtitle">AI-Powered Engagement</p>
        </header>

        <main class="popup-content">
            <p class="popup-description">
                Your AI assistant is ready! Generate smart comments and reposts directly on LinkedIn posts.
            </p>

            <ul class="popup-features">
                <li class="popup-feature">
                    <span class="popup-feature-icon">✓</span>
                    <span>Smart comment suggestions</span>
                </li>
                <li class="popup-feature">
                    <span class="popup-feature-icon">✓</span>
                    <span>Custom instructions support</span>
                </li>
                <li class="popup-feature">
                    <span class="popup-feature-icon">✓</span>
                    <span>Real-time data preview</span>
                </li>
                <li class="popup-feature">
                    <span class="popup-feature-icon">✓</span>
                    <span>Multiple themes available</span>
                </li>
            </ul>

            <button id="triggerQuotationBox" class="popup-button">
                <span class="popup-button-content">
                    <span class="popup-button-icon">✨</span>
                    <span>Try It Now</span>
                </span>
            </button>

            <div id="statusMessage" class="popup-status"></div>
        </main>

        <footer class="popup-footer">
            <p class="popup-footer-text">
                Click any comment or repost button on LinkedIn to get started
            </p>
        </footer>
    </div>

    <script src="popup.js"></script>
</body>
</html>