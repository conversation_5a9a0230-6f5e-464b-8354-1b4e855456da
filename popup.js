document.addEventListener('DOMContentLoaded', () => {
    const triggerButton = document.getElementById("triggerQuotationBox");
    const statusMessage = document.getElementById("statusMessage");

    // Function to show status message
    function showStatus(message, type = 'success') {
        if (statusMessage) {
            statusMessage.textContent = message;
            statusMessage.className = `popup-status ${type}`;
            statusMessage.style.display = 'block';
            
            // Auto-hide after 3 seconds
            setTimeout(() => {
                statusMessage.style.display = 'none';
            }, 3000);
        }
    }

    // Function to update button state
    function updateButtonState(loading = false) {
        if (!triggerButton) return;
        
        const buttonContent = triggerButton.querySelector('.popup-button-content');
        const icon = triggerButton.querySelector('.popup-button-icon');
        const text = buttonContent.querySelector('span:last-child');
        
        if (loading) {
            triggerButton.disabled = true;
            triggerButton.style.opacity = '0.7';
            icon.textContent = '⏳';
            text.textContent = 'Activating...';
        } else {
            triggerButton.disabled = false;
            triggerButton.style.opacity = '1';
            icon.textContent = '✨';
            text.textContent = 'Try It Now';
        }
    }

    if (triggerButton) {
        triggerButton.addEventListener("click", async () => {
            try {
                updateButtonState(true);
                
                // Get active tab
                const tabs = await new Promise((resolve) => {
                    chrome.tabs.query({ active: true, currentWindow: true }, resolve);
                });

                if (tabs.length === 0 || !tabs[0].id) {
                    throw new Error("No active tab found");
                }

                const tab = tabs[0];

                // Check if we're on LinkedIn
                if (!tab.url || !tab.url.includes('linkedin.com')) {
                    throw new Error("Please navigate to LinkedIn first");
                }

                // Execute script in the active tab
                await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    function: () => {
                        // Check if content script is loaded
                        if (typeof window.LinkedInAIAssistant_showQuotationBox === 'function') {
                            // Try to find a post container and show the quotation box
                            window.LinkedInAIAssistant_showQuotationBox('comment');
                            return { success: true, message: "AI Assistant activated!" };
                        } else {
                            return { 
                                success: false, 
                                message: "Extension not ready. Please refresh the LinkedIn page." 
                            };
                        }
                    }
                });

                showStatus("AI Assistant activated! Check the LinkedIn page.", 'success');
                
                // Close popup after a short delay
                setTimeout(() => {
                    window.close();
                }, 1500);

            } catch (error) {
                console.error("Error executing script:", error);
                
                let errorMessage = "Failed to activate AI Assistant";
                if (error.message.includes("LinkedIn")) {
                    errorMessage = "Please open LinkedIn first";
                } else if (error.message.includes("refresh")) {
                    errorMessage = "Please refresh the LinkedIn page";
                }
                
                showStatus(errorMessage, 'error');
            } finally {
                updateButtonState(false);
            }
        });
    } else {
        console.error("Trigger button not found in popup");
    }

    // Add keyboard support
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Enter' && triggerButton && !triggerButton.disabled) {
            triggerButton.click();
        }
    });

    // Add visual feedback for button interactions
    if (triggerButton) {
        triggerButton.addEventListener('mousedown', () => {
            triggerButton.style.transform = 'translateY(1px)';
        });

        triggerButton.addEventListener('mouseup', () => {
            triggerButton.style.transform = 'translateY(-2px)';
        });

        triggerButton.addEventListener('mouseleave', () => {
            triggerButton.style.transform = 'translateY(0)';
        });
    }
});