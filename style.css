/* LinkedIn AI Assistant - Modern UI Styles */

/* CSS Custom Properties for Design System */
:root {
  /* LinkedIn Brand Colors */
  --linkedin-blue: #0a66c2;
  --linkedin-blue-hover: #004182;
  --linkedin-blue-light: #378fe9;
  --linkedin-green: #057642;
  --linkedin-green-hover: #046139;
  
  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Semantic Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
  
  /* Spacing System (8px base) */
  --space-1: 0.25rem; /* 4px */
  --space-2: 0.5rem;  /* 8px */
  --space-3: 0.75rem; /* 12px */
  --space-4: 1rem;    /* 16px */
  --space-5: 1.25rem; /* 20px */
  --space-6: 1.5rem;  /* 24px */
  --space-8: 2rem;    /* 32px */
  --space-10: 2.5rem; /* 40px */
  --space-12: 3rem;   /* 48px */
  
  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-base: 1rem;    /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  
  /* Border Radius */
  --radius-sm: 0.25rem;  /* 4px */
  --radius-md: 0.375rem; /* 6px */
  --radius-lg: 0.5rem;   /* 8px */
  --radius-xl: 0.75rem;  /* 12px */
  --radius-2xl: 1rem;    /* 16px */
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 200ms ease;
  --transition-slow: 300ms ease;
}

/* Theme Variables with Enhanced Contrast */
.theme-modern {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-muted: #94a3b8;
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --accent-primary: var(--linkedin-blue);
  --accent-hover: var(--linkedin-blue-hover);
  --accent-text: #ffffff;
  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --input-focus: var(--linkedin-blue);
}

.theme-light {
  --bg-primary: #ffffff;
  --bg-secondary: #fafbfc;
  --bg-tertiary: #f1f3f4;
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --text-tertiary: #80868b;
  --text-muted: #9aa0a6;
  --border-primary: #dadce0;
  --border-secondary: #e8eaed;
  --accent-primary: var(--linkedin-blue);
  --accent-hover: var(--linkedin-blue-hover);
  --accent-text: #ffffff;
  --input-bg: #ffffff;
  --input-border: #dadce0;
  --input-focus: var(--linkedin-blue);
}

.theme-dark {
  --bg-primary: #121212;
  --bg-secondary: #1e1e1e;
  --bg-tertiary: #2a2a2a;
  --text-primary: #ffffff;
  --text-secondary: #e0e0e0;
  --text-tertiary: #b3b3b3;
  --text-muted: #888888;
  --border-primary: #404040;
  --border-secondary: #525252;
  --accent-primary: var(--linkedin-blue-light);
  --accent-hover: var(--linkedin-blue);
  --accent-text: #000000;
  --input-bg: #1e1e1e;
  --input-border: #525252;
  --input-focus: var(--linkedin-blue-light);
}

.theme-neon {
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;
  --text-primary: #00ff88;
  --text-secondary: #00e676;
  --text-tertiary: #00cc66;
  --text-muted: #009944;
  --border-primary: #00ff88;
  --border-secondary: #00cc66;
  --accent-primary: #00ffff;
  --accent-hover: #00e5e5;
  --accent-text: #000000;
  --input-bg: #1a1a1a;
  --input-border: #00cc66;
  --input-focus: #00ffff;
}

/* AI Enhancement Button Container */
.ai-enhancement-container {
  position: absolute;
  bottom: calc(100% + var(--space-2));
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  opacity: 1;
  transition: opacity var(--transition-normal), transform var(--transition-fast);
}

/* AI Enhancement Button */
.ai-enhancement-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  border: 1px solid var(--border-primary);
  background-color: var(--bg-primary);
  color: var(--accent-primary);
  font-size: var(--font-size-base);
  font-weight: 500;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.ai-enhancement-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
  opacity: 0;
  transition: opacity var(--transition-fast);
  z-index: -1;
}

.ai-enhancement-button:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
  border-color: var(--accent-primary);
}

.ai-enhancement-button:hover::before {
  opacity: 0.1;
}

.ai-enhancement-button:active {
  transform: scale(0.95);
}

.ai-enhancement-button-action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  line-height: 1;
}

/* Quotation Box - FIXED positioning and sizing */
#quotationBox {
  position: fixed;
  z-index: 10000;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  opacity: 0;
  transform: scale(0.9);
  transition: all var(--transition-normal);
  font-family: var(--font-family);
  color: var(--text-primary);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  /* FIXED: Better default dimensions */
  width: 480px;
  height: 600px;
  min-width: 320px;
  min-height: 400px;
  max-width: 95vw;
  max-height: 90vh;
  /* FIXED: Default positioning */
  top: 50px;
  left: 50px;
}

#quotationBox.visible {
  opacity: 1;
  transform: scale(1);
}

#quotationBox.dragging {
  cursor: grabbing;
  user-select: none;
  transition: none; /* Disable transitions while dragging */
}

/* FIXED: Responsive sizing for mobile */
@media (max-width: 768px) {
  #quotationBox {
    width: 95vw !important;
    height: 80vh !important;
    top: 10px !important;
    left: 2.5vw !important;
    max-width: none;
    max-height: none;
  }
}

@media (max-width: 480px) {
  #quotationBox {
    width: 98vw !important;
    height: 85vh !important;
    top: 5px !important;
    left: 1vw !important;
  }
}

/* Resize Handles - IMPROVED visibility */
.resize-handle {
  position: absolute;
  background-color: var(--accent-primary);
  opacity: 0.3;
  transition: opacity var(--transition-fast);
  z-index: 10;
}

#quotationBox:hover .resize-handle {
  opacity: 0.7;
}

.resize-handle:hover {
  opacity: 1 !important;
  background-color: var(--accent-hover) !important;
}

.resize-handle-se {
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  cursor: se-resize;
  border-radius: var(--radius-2xl) 0 var(--radius-2xl) 0;
}

.resize-handle-s {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 8px;
  cursor: s-resize;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.resize-handle-e {
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 40px;
  cursor: e-resize;
  border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

/* Header - IMPROVED draggable area */
.quotation-box-header-container {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  cursor: move;
  flex-shrink: 0;
  position: relative;
  user-select: none;
}

.quotation-box-header-container:active {
  cursor: grabbing;
}

.quotation-box-header {
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  position: relative;
  min-height: 60px;
}

/* IMPROVED drag handle indicator */
.quotation-box-header::before {
  content: '';
  position: absolute;
  top: 6px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 4px;
  background-color: var(--border-secondary);
  border-radius: var(--radius-full);
  opacity: 0.6;
}

.quotation-box-header:hover::before {
  opacity: 1;
  background-color: var(--accent-primary);
}

.quotation-box-icon {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-text);
  font-weight: 600;
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.quotation-box-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Header Actions Container */
.quotation-box-header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  flex-shrink: 0;
}

.quotation-box-settings-button {
  background: transparent;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-2);
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quotation-box-settings-button:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--accent-primary);
}

/* Close Button */
.quotation-box-close-button {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--font-size-lg);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all var(--transition-fast);
  width: 32px;
  height: 32px;
  padding: 0;
}

.quotation-box-close-button:hover {
  background-color: var(--error);
  color: white;
  border-color: var(--error);
  transform: scale(1.05);
}

/* Content Area - FIXED scrolling */
.quotation-box-content {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--border-secondary) transparent;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.quotation-box-content::-webkit-scrollbar {
  width: 8px;
}

.quotation-box-content::-webkit-scrollbar-track {
  background: transparent;
}

.quotation-box-content::-webkit-scrollbar-thumb {
  background-color: var(--border-secondary);
  border-radius: var(--radius-full);
}

.quotation-box-content::-webkit-scrollbar-thumb:hover {
  background-color: var(--border-primary);
}

/* Additional Instructions */
.additional-instructions-container {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
  flex-shrink: 0;
}

.additional-instructions-label {
  display: block;
  margin-bottom: var(--space-2);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.additional-instructions-input {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--input-border);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-family: inherit;
  color: var(--text-primary);
  background-color: var(--input-bg);
  resize: vertical;
  min-height: 60px;
  max-height: 100px;
  box-sizing: border-box;
  transition: all var(--transition-fast);
}

.additional-instructions-input:focus {
  outline: none;
  border-color: var(--input-focus);
  box-shadow: 0 0 0 3px rgba(10, 102, 194, 0.1);
}

.additional-instructions-input::placeholder {
  color: var(--text-muted);
  font-style: italic;
}

/* Generate Button */
.generate-suggestions-button {
  margin-top: var(--space-4);
  padding: var(--space-3) var(--space-6);
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
  color: var(--accent-text);
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: 600;
  transition: all var(--transition-fast);
  width: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.generate-suggestions-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, white, white);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.generate-suggestions-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.generate-suggestions-button:hover::before {
  opacity: 0.1;
}

.generate-suggestions-button:active {
  transform: translateY(0);
}

.generate-suggestions-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Loading Spinner */
.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-primary);
  border-top: 3px solid var(--accent-primary);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  margin: var(--space-8) auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Post Info */
.quotation-box-post-info {
  padding: var(--space-6);
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
  flex-shrink: 0;
  color: var(--text-primary);
}

.quotation-box-post-info strong {
  color: var(--text-primary);
  font-weight: 600;
}

.quotation-box-post-preview {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  font-style: italic;
  word-break: break-word;
  max-height: 100px;
  overflow-y: auto;
  padding: var(--space-3);
  background-color: var(--input-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--input-border);
  margin-top: var(--space-2);
}

/* Suggestions Container - FIXED scrolling */
.suggestion-container {
  padding: var(--space-6);
  flex: 1;
  overflow-y: auto;
  color: var(--text-primary);
  min-height: 200px;
}

/* Individual Suggestion Item - IMPROVED layout */
.suggestion-item {
  margin-bottom: var(--space-6);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  background-color: var(--bg-secondary);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.suggestion-item:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--accent-primary);
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
}

.suggestion-header h5 {
  margin: 0;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.suggestion-copy-button {
  padding: var(--space-2) var(--space-4);
  background: linear-gradient(135deg, var(--success), #059669);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--font-size-xs);
  font-weight: 600;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.suggestion-copy-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.suggestion-copy-button:active {
  transform: translateY(0);
}

.suggestion-text {
  padding: var(--space-4);
  font-size: var(--font-size-sm);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--input-bg);
  white-space: pre-wrap;
  word-wrap: break-word;
  min-height: 60px;
}

/* Insert Button for Comments */
.suggestion-insert-button {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--accent-primary);
  color: var(--accent-text);
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-weight: 600;
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  margin: var(--space-3);
  margin-top: 0;
}

.suggestion-insert-button:hover {
  background: var(--accent-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Settings Panel */
.settings-panel {
  padding: var(--space-6);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-xl);
  margin: var(--space-4);
  border: 1px solid var(--border-primary);
}

.settings-group {
  margin-bottom: var(--space-6);
}

.settings-label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 600;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.settings-select {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--input-border);
  border-radius: var(--radius-lg);
  background-color: var(--input-bg);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  box-sizing: border-box;
  transition: all var(--transition-fast);
}

.settings-select:focus {
  outline: none;
  border-color: var(--input-focus);
  box-shadow: 0 0 0 3px rgba(10, 102, 194, 0.1);
}

.settings-position-options {
  display: flex;
  gap: var(--space-3);
}

.settings-position-option {
  flex: 1;
  padding: var(--space-4);
  text-align: center;
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  cursor: pointer;
  background-color: var(--input-bg);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all var(--transition-fast);
}

.settings-position-option:hover {
  border-color: var(--accent-primary);
  color: var(--text-primary);
}

.settings-position-option.selected {
  border-color: var(--accent-primary);
  background-color: var(--accent-primary);
  color: var(--accent-text);
}

.settings-save-button {
  width: 100%;
  padding: var(--space-4);
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
  color: var(--accent-text);
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-weight: 600;
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  margin-top: var(--space-4);
}

.settings-save-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.settings-reset-button {
  width: 100%;
  padding: var(--space-3);
  background: var(--gray-600);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all var(--transition-fast);
  margin-bottom: var(--space-3);
}

.settings-reset-button:hover {
  background: var(--gray-700);
  transform: translateY(-1px);
}

/* LinkedIn Ideas Button */
#linkedin-ai-ideas-button {
  position: fixed;
  top: 80px;
  right: var(--space-5);
  z-index: 9999;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
  color: var(--accent-text);
  border: none;
  border-radius: var(--radius-2xl);
  padding: var(--space-3) var(--space-5);
  font-size: var(--font-size-sm);
  font-weight: 600;
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
}

#linkedin-ai-ideas-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

#linkedin-ai-ideas-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

#linkedin-ai-ideas-button-icon {
  font-size: var(--font-size-base);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  #linkedin-ai-ideas-button {
    padding: var(--space-3);
    right: var(--space-4);
  }
  
  #linkedin-ai-ideas-button-text {
    display: none;
  }
  
  .additional-instructions-container,
  .quotation-box-post-info,
  .suggestion-container {
    padding: var(--space-4);
  }
  
  .quotation-box-header {
    padding: var(--space-4);
  }
  
  /* Hide resize handles on mobile */
  .resize-handle {
    display: none;
  }
  
  /* Ensure mobile quotation box is properly sized */
  #quotationBox {
    border-radius: var(--radius-lg) !important;
  }
}

/* Confetti Animation */
.confetti {
  position: fixed;
  width: 8px;
  height: 8px;
  left: 0;
  top: -10px;
  z-index: 10000;
  border-radius: var(--radius-full);
  opacity: 1;
  animation: fall 3s linear forwards;
}

@keyframes fall {
  to {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

/* Custom Alert */
.custom-alert-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20000;
  opacity: 0;
  transition: opacity var(--transition-normal);
  pointer-events: none;
  backdrop-filter: blur(4px);
}

.custom-alert-overlay.visible {
  opacity: 1;
  pointer-events: auto;
}

.custom-alert-box {
  background-color: var(--bg-primary);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  text-align: center;
  max-width: 400px;
  width: 90%;
  transform: scale(0.9);
  transition: transform var(--transition-normal);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}

.custom-alert-overlay.visible .custom-alert-box {
  transform: scale(1);
}

.custom-alert-message {
  font-size: var(--font-size-base);
  margin-bottom: var(--space-6);
  line-height: 1.5;
  color: var(--text-primary);
}

.custom-alert-button {
  padding: var(--space-3) var(--space-6);
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
  color: var(--accent-text);
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: 600;
  transition: all var(--transition-fast);
}

.custom-alert-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Separators */
.suggestions-separator {
  margin: var(--space-6) 0;
  border-top: 1px dashed var(--border-secondary);
  position: relative;
}

.suggestions-separator-label {
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  padding: 0 var(--space-3);
  background-color: var(--bg-primary);
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Accessibility */
.visually-hidden {
  position: absolute;
  left: -10000px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Focus Indicators */
*:focus-visible {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --border-primary: #000000;
    --border-secondary: #000000;
  }
  
  .theme-dark {
    --border-primary: #ffffff;
    --border-secondary: #ffffff;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print Styles */
@media print {
  #quotationBox,
  #linkedin-ai-ideas-button,
  .ai-enhancement-container {
    display: none !important;
  }
}

/* Enhanced Text Visibility for All Themes */
.quotation-box-post-info div {
  color: var(--text-secondary);
}

.quotation-box-post-info div strong {
  color: var(--text-primary);
}

.suggestion-container div {
  color: var(--text-primary);
}

/* Ensure all text elements have proper contrast */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
}

p, span, div {
  color: inherit;
}

label {
  color: var(--text-primary);
}

/* Input placeholder visibility */
input::placeholder,
textarea::placeholder {
  color: var(--text-muted);
  opacity: 1;
}

/* Button text visibility */
button {
  color: inherit;
}

/* Specific fixes for dark and neon themes */
.theme-dark .quotation-box-post-info div,
.theme-neon .quotation-box-post-info div {
  opacity: 0.9;
}

.theme-dark .suggestion-container,
.theme-neon .suggestion-container {
  color: var(--text-primary);
}

/* Ensure proper contrast for all interactive elements */
.theme-dark .suggestion-button,
.theme-neon .suggestion-button {
  color: var(--text-primary);
  background-color: var(--bg-secondary);
}

.theme-dark .suggestion-textarea,
.theme-neon .suggestion-textarea {
  color: var(--text-primary);
  background-color: var(--input-bg);
}