name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

jobs:
  lint-and-validate:
    name: <PERSON><PERSON> and <PERSON><PERSON><PERSON>
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: |
        npm install -g eslint web-ext
        
    - name: Validate manifest.json
      run: |
        node -e "
          const manifest = require('./manifest.json');
          console.log('✅ Manifest is valid JSON');
          console.log('Extension name:', manifest.name);
          console.log('Version:', manifest.version);
          if (!manifest.manifest_version) throw new Error('Missing manifest_version');
          if (!manifest.name) throw new Error('Missing name');
          if (!manifest.version) throw new Error('Missing version');
        "
        
    - name: Lint JavaScript files
      run: |
        # Basic syntax check for JavaScript files
        node -c contentscript.js
        node -c popup.js
        echo "✅ JavaScript syntax is valid"
        
    - name: Validate CSS
      run: |
        # Check CSS syntax
        if command -v csslint &> /dev/null; then
          csslint style.css
        else
          echo "⚠️ CSS linting skipped (csslint not available)"
        fi
        
    - name: Check file structure
      run: |
        echo "📁 Checking required files..."
        required_files=("manifest.json" "contentscript.js" "style.css" "popup.html" "popup.js")
        for file in "${required_files[@]}"; do
          if [ -f "$file" ]; then
            echo "✅ $file exists"
          else
            echo "❌ $file is missing"
            exit 1
          fi
        done
        
    - name: Validate extension with web-ext
      run: |
        web-ext lint --source-dir . --ignore-files="node_modules/**" "*.md" ".git/**" ".github/**"

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run security checks
      run: |
        echo "🔒 Running security checks..."
        
        # Check for sensitive data in files
        echo "Checking for potential secrets..."
        if grep -r -i "password\|secret\|key\|token" --include="*.js" --include="*.json" .; then
          echo "⚠️ Potential sensitive data found - please review"
        else
          echo "✅ No obvious sensitive data found"
        fi
        
        # Check permissions in manifest
        echo "Checking manifest permissions..."
        node -e "
          const manifest = require('./manifest.json');
          const permissions = manifest.permissions || [];
          console.log('Permissions:', permissions);
          
          const dangerousPerms = ['tabs', 'history', 'bookmarks', 'downloads'];
          const found = permissions.filter(p => dangerousPerms.includes(p));
          if (found.length > 0) {
            console.log('⚠️ Potentially sensitive permissions:', found);
          } else {
            console.log('✅ No sensitive permissions detected');
          }
        "

  build-and-package:
    name: Build and Package
    runs-on: ubuntu-latest
    needs: [lint-and-validate, security-scan]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Create extension package
      run: |
        echo "📦 Creating extension package..."
        
        # Create build directory
        mkdir -p build
        
        # Copy extension files (exclude development files)
        cp manifest.json build/
        cp contentscript.js build/
        cp style.css build/
        cp popup.html build/
        cp popup.js build/
        cp README.md build/
        
        # Create zip package
        cd build
        zip -r ../linkedin-ai-assistant-extension.zip .
        cd ..
        
        echo "✅ Extension packaged successfully"
        ls -la linkedin-ai-assistant-extension.zip
        
    - name: Upload build artifact
      uses: actions/upload-artifact@v4
      with:
        name: extension-package
        path: linkedin-ai-assistant-extension.zip
        retention-days: 30

  test-extension:
    name: Test Extension
    runs-on: ubuntu-latest
    needs: [build-and-package]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Install testing dependencies
      run: |
        npm install -g web-ext
        
    - name: Download build artifact
      uses: actions/download-artifact@v4
      with:
        name: extension-package
        
    - name: Extract and test extension
      run: |
        echo "🧪 Testing extension..."
        
        # Extract the package
        unzip linkedin-ai-assistant-extension.zip -d test-extension/
        
        # Run web-ext lint on the packaged extension
        web-ext lint --source-dir test-extension/
        
        echo "✅ Extension tests passed"

  release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: [test-extension]
    if: github.event_name == 'release'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download build artifact
      uses: actions/download-artifact@v4
      with:
        name: extension-package
        
    - name: Upload release asset
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: ./linkedin-ai-assistant-extension.zip
        asset_name: linkedin-ai-assistant-extension-${{ github.event.release.tag_name }}.zip
        asset_content_type: application/zip
