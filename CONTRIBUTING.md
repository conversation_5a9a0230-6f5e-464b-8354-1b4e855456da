# Contributing to LinkedIn AI Assistant Chrome Extension

Thank you for your interest in contributing! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Reporting Issues
1. **Search existing issues** first to avoid duplicates
2. **Use issue templates** when available
3. **Provide detailed information**:
   - Chrome version
   - Extension version
   - Steps to reproduce
   - Expected vs actual behavior
   - Screenshots if applicable

### Suggesting Features
1. **Check existing feature requests** first
2. **Describe the use case** clearly
3. **Explain the benefit** to users
4. **Consider implementation complexity**

### Code Contributions

#### Getting Started
1. **Fork the repository**
2. **Clone your fork**:
   ```bash
   git clone https://github.com/YOUR_USERNAME/linkedin-ai-assistant.git
   cd linkedin-ai-assistant
   ```
3. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

#### Development Setup
1. **Load extension in Chrome**:
   - Open `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked"
   - Select the project directory

2. **Make your changes**
3. **Test thoroughly**:
   - Test on different LinkedIn pages
   - Test with different themes
   - Test error scenarios

#### Code Standards
- **JavaScript**: Use modern ES6+ syntax
- **Comments**: Add clear comments for complex logic
- **Error Handling**: Always include proper error handling
- **Console Logging**: Use meaningful log messages
- **Performance**: Avoid blocking operations

#### Commit Guidelines
Use conventional commit format:
```
type(scope): description

feat(comments): add custom instructions field
fix(capture): resolve stale data issue
docs(readme): update installation instructions
style(themes): improve dark theme contrast
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

#### Pull Request Process
1. **Update documentation** if needed
2. **Add tests** for new features
3. **Ensure CI passes**
4. **Request review** from maintainers
5. **Address feedback** promptly

## 🧪 Testing

### Manual Testing
- **Basic functionality**: Comment and repost generation
- **Data capture**: Verify correct post and comments are captured
- **UI/UX**: Test all themes and positions
- **Error handling**: Test with network issues, invalid webhooks
- **Performance**: Test on pages with many posts/comments

### Browser Testing
Test on:
- **Chrome** (primary target)
- **Edge** (Chromium-based)
- **Brave** (Chromium-based)

### LinkedIn Testing
Test on different LinkedIn pages:
- **Feed**: Main LinkedIn feed
- **Post details**: Individual post pages
- **Company pages**: Posts on company pages
- **Profile pages**: Posts on user profiles

## 📝 Documentation

### Code Documentation
- **Functions**: Document parameters and return values
- **Complex logic**: Explain the "why" not just the "what"
- **APIs**: Document webhook integration clearly
- **Configuration**: Document all settings and options

### User Documentation
- **README**: Keep installation and usage instructions current
- **Changelog**: Document all changes in releases
- **Troubleshooting**: Add common issues and solutions

## 🔒 Security Guidelines

### Data Handling
- **No persistent storage** of LinkedIn data
- **Minimal data collection** - only what's necessary
- **Secure transmission** to webhooks only
- **No tracking** or analytics

### Code Security
- **Input validation** for all user inputs
- **XSS prevention** in DOM manipulation
- **CSP compliance** with extension security policies
- **Permission minimization** in manifest.json

## 🎨 Design Guidelines

### UI/UX Principles
- **LinkedIn integration**: Match LinkedIn's design language
- **Accessibility**: Support keyboard navigation and screen readers
- **Performance**: Minimal impact on page load and interaction
- **Responsiveness**: Work on different screen sizes

### Theme System
- **Consistency**: Maintain consistent styling across themes
- **Contrast**: Ensure good contrast ratios for accessibility
- **Customization**: Allow user preference configuration

## 🚀 Release Process

### Version Numbering
Follow semantic versioning (semver):
- **Major** (x.0.0): Breaking changes
- **Minor** (0.x.0): New features, backward compatible
- **Patch** (0.0.x): Bug fixes, backward compatible

### Release Checklist
- [ ] Update version in manifest.json
- [ ] Update CHANGELOG.md
- [ ] Test on multiple LinkedIn pages
- [ ] Verify CI/CD pipeline passes
- [ ] Create GitHub release
- [ ] Update Chrome Web Store listing

## 📞 Communication

### Channels
- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Pull Requests**: Code review and collaboration

### Response Times
- **Issues**: We aim to respond within 48 hours
- **Pull Requests**: Initial review within 72 hours
- **Security Issues**: Immediate attention (email maintainers)

## 🏆 Recognition

Contributors will be:
- **Listed in CONTRIBUTORS.md**
- **Mentioned in release notes**
- **Credited in significant feature announcements**

## ❓ Questions?

If you have questions about contributing:
1. **Check existing documentation** first
2. **Search GitHub issues** for similar questions
3. **Create a new discussion** for general questions
4. **Create an issue** for specific problems

Thank you for contributing to make LinkedIn AI Assistant better for everyone! 🎉
