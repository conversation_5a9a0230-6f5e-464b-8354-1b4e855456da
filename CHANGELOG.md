# Changelog

All notable changes to the LinkedIn AI Assistant Chrome Extension will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [2.0.0] - 2024-12-19

### ✨ Added
- **Custom Instructions Field**: Users can now add personalized instructions for comment generation (e.g., "Make it more casual", "Add emojis")
- **Wait-for-Generate Flow**: Comments now wait for user input before sending webhook, giving users control over when AI suggestions are generated
- **Enhanced Comment Capture**: Increased comment capture limit from 10 to 50 comments for better context
- **Real-time Data Preview**: Users can now see exactly what post content and comments are captured before generating suggestions
- **Fresh Data Capture**: Extension now always captures current post data, eliminating stale cache issues
- **Smart UI Display**: Shows first 10 comments in detail with summary of additional comments
- **Enhanced Debugging**: Added comprehensive logging for troubleshooting data capture issues

### 🐛 Fixed
- **Icon Dependencies**: Removed references to non-existent icon files from manifest.json
- **Timing Issues**: Fixed data capture timing to ensure UI displays captured data correctly
- **Stale Data Problem**: Resolved issue where extension showed comments from previous posts
- **Header Structure**: Updated CSS selectors after header structure changes
- **Post Container References**: Improved post container detection to always work with correct post

### 🎨 Improved
- **Visual Feedback**: Enhanced UI to show loading states and generation progress
- **Data Validation**: Added post ID verification to ensure correct data capture
- **Error Handling**: Improved error handling and recovery mechanisms
- **User Experience**: Better visual indication of captured data and processing status

### 🔧 Technical
- **Code Organization**: Refactored data capture logic for better maintainability
- **Performance**: Optimized comment capture to handle larger numbers efficiently
- **Reliability**: Added fresh container lookup to prevent stale references

## [1.0.0] - 2024-12-01

### ✨ Added
- **Initial Release**: Basic comment and repost generation functionality
- **LinkedIn Integration**: Seamless integration with LinkedIn's interface
- **Webhook Support**: Send captured data to custom webhook endpoints
- **Theme System**: Multiple themes (Modern, Light, Dark, Neon) for user preference
- **Flexible Positioning**: Choose left or right side placement of the extension UI
- **Data Capture**: Capture LinkedIn post content and existing comments
- **AI Integration**: Send structured data to AI services for suggestion generation
- **Settings Panel**: User-friendly configuration interface
- **Error Handling**: Basic error handling and user feedback
- **Accessibility**: Keyboard navigation and screen reader support

### 🔧 Technical
- **Content Script**: Main functionality for LinkedIn page interaction
- **Popup Interface**: Extension settings and configuration
- **CSS Themes**: Dynamic styling system
- **Manifest V3**: Modern Chrome extension architecture
- **Security**: Privacy-focused design with no data storage

---

## Version History Summary

- **v2.0.0**: Major update with custom instructions, enhanced data capture, and improved user experience
- **v1.0.0**: Initial release with core functionality

## Migration Guide

### From v1.0.0 to v2.0.0

**New Features Available:**
- Custom instructions field for comments (optional)
- Enhanced comment capture (up to 50 comments)
- Real-time data preview in quotation box

**Breaking Changes:**
- None - all existing functionality remains compatible

**Recommended Actions:**
1. Update to v2.0.0 for enhanced features
2. Test the new custom instructions field
3. Verify webhook still receives data correctly (new optional field added)

## Support

For questions about changes or upgrade issues:
- Check the [README.md](README.md) for updated documentation
- Review [GitHub Issues](https://github.com/your-repo/issues) for known issues
- Create a new issue if you encounter problems
