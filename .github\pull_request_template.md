## 📋 Description
Brief description of what this PR does.

## 🔗 Related Issue
Fixes #(issue number)

## 🎯 Type of Change
- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🎨 Style/UI changes
- [ ] ♻️ Code refactoring
- [ ] ⚡ Performance improvement
- [ ] 🧪 Test updates

## 🧪 Testing
- [ ] I have tested this change locally
- [ ] I have tested on multiple LinkedIn pages
- [ ] I have tested with different themes
- [ ] I have tested error scenarios
- [ ] I have tested the webhook integration

## 📝 Changes Made
### Files Modified
- `file1.js` - Description of changes
- `file2.css` - Description of changes
- `file3.html` - Description of changes

### Key Changes
1. **Change 1**: Description of what was changed and why
2. **Change 2**: Description of what was changed and why
3. **Change 3**: Description of what was changed and why

## 🔍 Testing Instructions
1. Load the extension in Chrome
2. Navigate to LinkedIn
3. Test specific functionality:
   - [ ] Comment generation works
   - [ ] Repost generation works
   - [ ] Data capture is accurate
   - [ ] UI displays correctly
   - [ ] Webhook receives correct data

## 📸 Screenshots (if applicable)
Before:
[Screenshot of before state]

After:
[Screenshot of after state]

## ⚠️ Breaking Changes
List any breaking changes and migration steps needed.

## 📚 Documentation
- [ ] README updated (if needed)
- [ ] Code comments added/updated
- [ ] CHANGELOG updated
- [ ] API documentation updated (if applicable)

## ✅ Checklist
- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

## 🔒 Security Considerations
- [ ] No sensitive data is exposed
- [ ] Input validation is implemented
- [ ] XSS prevention measures are in place
- [ ] Permissions are minimal and necessary

## 📊 Performance Impact
- [ ] No performance degradation
- [ ] Performance improvement
- [ ] Minor performance impact (acceptable)
- [ ] Significant performance impact (needs discussion)

## 🤔 Additional Notes
Any additional information, concerns, or questions for reviewers.
